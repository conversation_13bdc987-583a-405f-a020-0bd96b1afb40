import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { HeroSection } from "@/components/landing-page/HeroSection";
import { ComparisonSection } from "@/components/landing-page/ComparisonSection";
import { FeaturesSection } from "@/components/landing-page/FeaturesSection";
import { PricingSection } from "@/components/landing-page/PricingSection";
import { ROISection } from "@/components/landing-page/ROISection";
import { FooterSection } from "@/components/landing-page/FooterSection";

export default async function Home() {
  // If user is already signed in, redirect immediately to /create
  const { userId } = await auth();
  if (userId) {
    redirect("/create");
  }

  // Otherwise, show the landing page
  return (
    <main className="min-h-screen bg-white overflow-x-hidden">
      <HeroSection />
      <ComparisonSection />
      <FeaturesSection />
      <ROISection />
      <PricingSection />
      <FooterSection />
    </main>
  );
}