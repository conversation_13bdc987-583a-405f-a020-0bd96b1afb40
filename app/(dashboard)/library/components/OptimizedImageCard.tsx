"use client";

import { useState, memo, useCallback, useEffect } from 'react';
import { Trash2, Info, Download, X } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';
import { useImageLazyLoading } from '@/hooks/useImageLazyLoading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface OptimizedImageCardProps {
  id: number;
  imageUrl: string;
  createdAt: string;
  prompt?: string;
  isSelectMode: boolean;
  isSelected: boolean;
  onDelete?: (id: number) => void;
  onSelect?: (id: number) => void;
  onImageClickForDelete?: (id: number) => void;
  index?: number;
}

const OptimizedImageCard = memo(function OptimizedImageCard({
  id,
  imageUrl,
  createdAt,
  prompt,
  isSelectMode,
  isSelected,
  onDelete,
  onSelect,
  onImageClickForDelete,
  index = 0
}: OptimizedImageCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const { isMobile } = useResponsive();
  
  // Use lazy loading with preload for first few images
  const { isLoading, isLoaded, error, shouldLoad, imgRef } = useImageLazyLoading(
    imageUrl,
    {
      rootMargin: '100px',
      threshold: 0.1,
      enablePreload: true,
      preloadCount: 4
    }
  );

  const handleDelete = useCallback(async () => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/images/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete image');
      }
      
      onDelete?.(id);
    } catch (error) {
      console.error('Error deleting image:', error);
      setIsErrorDialogOpen(true);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  }, [id, onDelete]);

  const handleDownload = useCallback(async () => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `market-me-image-${id}-${Date.now()}.webp`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  }, [imageUrl, id]);

  const toggleExpand = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const handleImageClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isSelectMode) {
      // In normal mode, clicking image expands it
      toggleExpand();
    }
    // In select mode, clicking is handled by the card wrapper (for selection)
  }, [isSelectMode, toggleExpand]);

  const handleImageRightClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isSelectMode) {
      // Right-click triggers delete confirmation
      onImageClickForDelete?.(id);
    }
  }, [isSelectMode, onImageClickForDelete, id]);

  // Long press handler for mobile
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);

  const handleTouchStart = useCallback(() => {
    if (!isSelectMode) {
      const timer = setTimeout(() => {
        // Long press triggers delete confirmation
        onImageClickForDelete?.(id);
      }, 500); // 500ms long press
      setLongPressTimer(timer);
    }
  }, [isSelectMode, onImageClickForDelete, id]);

  const handleTouchEnd = useCallback(() => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [longPressTimer]);

  const handleCardClick = useCallback(() => {
    if (isSelectMode) {
      onSelect?.(id);
    }
  }, [isSelectMode, onSelect, id]);

  const handleInfoClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsInfoDialogOpen(true);
  }, []);

  const handleDownloadClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleDownload();
  }, [handleDownload]);

  const handleDeleteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  }, []);

  // Handle body overflow for expanded view
  useEffect(() => {
    if (isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded]);

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
    };
  }, [longPressTimer]);

  const formattedDate = new Date(createdAt).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  return (
    <>
      <div 
        className={`bg-white border rounded-[25px] overflow-hidden transition-all ${
          isSelected ? 'border-[#FD2D55] ring-2 ring-[#FD2D55]' : 'border-gray-200 hover:shadow-md'
        } ${isSelectMode ? 'cursor-pointer' : ''}`}
        onClick={handleCardClick}
        data-image-index={index}
      >
        <div className="relative overflow-hidden aspect-square">
          {/* Skeleton/Loading state */}
          {(isLoading || !shouldLoad) && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            </div>
          )}
          
          {/* Image */}
          <img
            ref={imgRef}
            src={shouldLoad ? imageUrl : undefined}
            alt="Generated image"
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } ${!isSelectMode ? 'cursor-pointer' : ''}`}
            loading="lazy"
            onClick={handleImageClick}
            onContextMenu={handleImageRightClick}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchCancel={handleTouchEnd}
            onError={() => console.error(`Failed to load image: ${imageUrl}`)}
          />

          {/* Error state */}
          {error && (
            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="text-sm">Failed to load</div>
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    window.location.reload();
                  }}
                  className="text-xs text-blue-500 hover:underline mt-1"
                >
                  Retry
                </button>
              </div>
            </div>
          )}
          
          {/* Selection overlay */}
          {isSelected && (
            <div className="absolute inset-0 bg-[#FD2D55]/10 flex items-center justify-center">
              <div className={`${isMobile ? 'w-8 h-8' : 'w-6 h-6'} bg-[#FD2D55] rounded-full flex items-center justify-center`}>
                <svg width={isMobile ? "16" : "12"} height={isMobile ? "12" : "9"} viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.5 6.6L10.6 0.5L11.7 1.6L4.5 8.8L0.5 4.8L1.6 3.7L4.5 6.6Z" fill="white"/>
                </svg>
              </div>
            </div>
          )}
        </div>
        
        <div className={`${isMobile ? 'p-3' : 'p-4'}`}>
          <div className={`flex justify-between items-start ${isMobile ? 'gap-2' : ''}`}>
            <div className="flex-1">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>{formattedDate}</p>
            </div>
            
            {!isSelectMode && (
              <div className={`flex ${isMobile ? 'gap-1' : ''}`}>
                {prompt && (
                  <button
                    className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-gray-700 rounded-lg flex items-center justify-center`}
                    onClick={handleInfoClick}
                    title="Show details"
                  >
                    <Info size={isMobile ? 20 : 18} />
                  </button>
                )}
                <button
                  className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-[#FD2D55] rounded-lg flex items-center justify-center`}
                  onClick={handleDownloadClick}
                  title="Download image"
                >
                  <Download size={isMobile ? 20 : 18} />
                </button>
                {onDelete && (
                  <button
                    className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-red-600 rounded-lg flex items-center justify-center`}
                    onClick={handleDeleteClick}
                    disabled={isDeleting}
                    title="Delete image"
                  >
                    <Trash2 size={isMobile ? 20 : 18} />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Dialogs remain the same but optimized with useCallback */}
      {prompt && (
        <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
          <DialogContent className={`${isMobile ? 'max-w-[95vw] max-h-[90vh] overflow-auto' : ''}`}>
            <DialogHeader>
              <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Image Details</DialogTitle>
            </DialogHeader>
            <div className={`${isMobile ? 'space-y-4' : 'grid grid-cols-1 md:grid-cols-2 gap-4'}`}>
              <div className={`${isMobile ? 'w-full aspect-square' : 'aspect-square'} relative rounded-md overflow-hidden`}>
                <img
                  src={imageUrl}
                  alt="Generated image"
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="flex flex-col space-y-3">
                <div>
                  <h4 className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-500`}>Created on</h4>
                  <p className={`${isMobile ? 'text-base' : ''}`}>{formattedDate}</p>
                </div>
                <div>
                  <h4 className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-500`}>Prompt</h4>
                  <p className={`${isMobile ? 'text-sm leading-relaxed' : 'text-sm'} break-words mt-1`}>{prompt}</p>
                </div>
              </div>
            </div>
            <DialogFooter className={`${isMobile ? 'mt-6' : ''}`}>
              <button
                onClick={() => setIsInfoDialogOpen(false)}
                className={`${isMobile ? 'w-full py-3 text-base' : 'px-4 py-2 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors`}
              >
                Close
              </button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Confirm Deletion</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              Are you sure you want to delete this image?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'flex-col gap-3 mt-6' : ''}`}>
            <button
              onClick={() => setIsDeleteDialogOpen(false)}
              className={`${isMobile ? 'w-full py-3 text-base order-2' : 'px-4 py-2 text-sm'} bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors`}
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className={`${isMobile ? 'w-full py-3 text-base order-1' : 'px-4 py-2 text-sm'} bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors`}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Error</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              Failed to delete image. Please try again.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'mt-6' : ''}`}>
            <button
              onClick={() => setIsErrorDialogOpen(false)}
              className={`${isMobile ? 'w-full py-3 text-base' : 'px-4 py-2 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors`}
            >
              OK
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Expanded View */}
      {isExpanded && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4" onClick={toggleExpand}>
          <div className="relative max-w-7xl w-full max-h-[95vh] flex flex-col gap-4">
            <button
              onClick={toggleExpand}
              className="absolute -top-12 right-0 p-2 text-white hover:text-gray-300"
              aria-label="Close expanded view"
            >
              <X className="w-6 h-6" />
            </button>

            <div className="flex-1 flex items-center justify-center overflow-auto min-h-[50vh] bg-white rounded-lg">
              <img
                src={imageUrl}
                alt="Generated image"
                className="max-w-full max-h-[90vh] object-contain"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
});

export default OptimizedImageCard;