"use client";

import { memo, useCallback, useMemo } from 'react';
import { useResponsive } from '@/hooks/useResponsive';
import { useInfiniteScroll } from '@/hooks/useImageLazyLoading';
import OptimizedImageCard from './OptimizedImageCard';

interface Image {
  id: number;
  image_url: string;
  aspect_ratio?: string;
  created_at: string;
  prompt?: string;
}

interface OptimizedImageGridProps {
  images: Image[];
  selectedIds: number[];
  isSelectMode: boolean;
  onImageSelected: (id: number) => void;
  onImageDeleted: (id: number) => void;
  onLoadMore?: () => Promise<void>;
  hasMore?: boolean;
  isLoading?: boolean;
}

const OptimizedImageGrid = memo(function OptimizedImageGrid({
  images,
  selectedIds,
  isSelectMode,
  onImageSelected,
  onImageDeleted,
  onLoadMore,
  hasMore = false,
  isLoading = false
}: OptimizedImageGridProps) {
  const { isMobile, isTablet } = useResponsive();
  
  console.log("[OptimizedImageGrid] Rendering with", images.length, "images, isSelectMode:", isSelectMode);
  
  // Memoized grid configuration
  const gridConfig = useMemo(() => {
    if (isMobile) return { cols: 'grid-cols-1 sm:grid-cols-2', gap: 'gap-4' };
    if (isTablet) return { cols: 'grid-cols-2 md:grid-cols-3', gap: 'gap-6' };
    return { cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4', gap: 'gap-6' };
  }, [isMobile, isTablet]);

  // Memoized selected ids set for O(1) lookup
  const selectedIdsSet = useMemo(() => new Set(selectedIds), [selectedIds]);

  // Optimized handlers with useCallback
  const handleImageSelect = useCallback((id: number) => {
    onImageSelected(id);
  }, [onImageSelected]);

  const handleImageDelete = useCallback((id: number) => {
    onImageDeleted(id);
  }, [onImageDeleted]);

  // Infinite scroll hook
  const { sentinelRef, isFetching } = useInfiniteScroll(
    async () => {
      if (onLoadMore && hasMore && !isLoading) {
        await onLoadMore();
      }
    },
    hasMore,
    isLoading,
    200 // Load more when 200px from bottom
  );

  // Memoized image items to prevent unnecessary re-renders
  const imageItems = useMemo(() => 
    images.map((image, index) => (
      <OptimizedImageCard
        key={image.id}
        id={image.id}
        imageUrl={image.image_url}
        createdAt={image.created_at}
        prompt={image.prompt}
        isSelectMode={isSelectMode}
        isSelected={selectedIdsSet.has(image.id)}
        onSelect={handleImageSelect}
        onDelete={handleImageDelete}
        index={index}
      />
    )),
    [images, isSelectMode, selectedIdsSet, handleImageSelect, handleImageDelete]
  );

  return (
    <div className="space-y-6">
      {/* Main Grid */}
      <div className={`grid ${gridConfig.cols} ${gridConfig.gap}`}>
        {imageItems}
      </div>
      
      {/* Loading State for Additional Items */}
      {(isLoading || isFetching) && (
        <div className="flex justify-center items-center py-8">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            <span className="text-gray-600">Loading more images...</span>
          </div>
        </div>
      )}

      {/* Infinite Scroll Sentinel */}
      {hasMore && !isLoading && (
        <div 
          ref={sentinelRef} 
          className="h-10 flex justify-center items-center"
          aria-hidden="true"
        >
          {/* Invisible sentinel for intersection observer */}
        </div>
      )}

      {/* No More Images Message */}
      {!hasMore && images.length > 0 && !isLoading && (
        <div className="text-center py-8 text-gray-500">
          <p className={`${isMobile ? 'text-base' : ''}`}>No more images to load</p>
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && !isLoading && (
        <div className={`text-center ${isMobile ? 'py-8' : 'py-10'} text-gray-500`}>
          <p className={`${isMobile ? 'text-xl' : 'text-lg'}`}>Your library is empty</p>
          <p className={`mt-2 ${isMobile ? 'text-base' : ''}`}>Create new images in the Create tab</p>
        </div>
      )}
    </div>
  );
});

export default OptimizedImageGrid;