"use client";

import { useState } from 'react';
import { Trash2, Info, Download } from 'lucide-react';
import GeneratedImage from '../../create/components/GeneratedImage';
import { useResponsive } from '@/hooks/useResponsive';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ImageCardProps {
  id: number;
  imageUrl: string;
  createdAt: string;
  prompt?: string;
  isSelectMode: boolean;
  isSelected: boolean;
  onDelete?: (id: number) => void;
}

export default function ImageCard({ 
  id, 
  imageUrl, 
  createdAt, 
  prompt,
  isSelectMode,
  isSelected,
  onDelete
}: ImageCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  
  const formattedDate = new Date(createdAt).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/images/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete image');
      }
      
      onDelete?.(id);
    } catch (error) {
      console.error('Error deleting image:', error);
      setIsErrorDialogOpen(true);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `market-me-image-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const { isMobile } = useResponsive();
  
  return (
    <>
      <div className={`bg-white border rounded-[25px] overflow-hidden transition-all ${isSelected ? 'border-[#FD2D55] ring-2 ring-[#FD2D55]' : 'border-gray-200 hover:shadow-md'}`}>
        <div className="relative overflow-hidden">
          <GeneratedImage
            src={imageUrl}
            alt={`Generated image`}
            showActions={!isSelectMode}
          />
          {isSelected && (
            <div className="absolute inset-0 bg-[#FD2D55]/10 flex items-center justify-center">
              <div className={`${isMobile ? 'w-8 h-8' : 'w-6 h-6'} bg-[#FD2D55] rounded-full flex items-center justify-center`}>
                <svg width={isMobile ? "16" : "12"} height={isMobile ? "12" : "9"} viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.5 6.6L10.6 0.5L11.7 1.6L4.5 8.8L0.5 4.8L1.6 3.7L4.5 6.6Z" fill="white"/>
                </svg>
              </div>
            </div>
          )}
        </div>
        
        <div className={`${isMobile ? 'p-3' : 'p-4'}`}>
          <div className={`flex justify-between items-start ${isMobile ? 'gap-2' : ''}`}>
            <div className="flex-1">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>{formattedDate}</p>
            </div>
            
            {!isSelectMode && (
              <div className={`flex ${isMobile ? 'gap-1' : ''}`}>
                {prompt && (
                  <button
                    className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-gray-700 rounded-lg flex items-center justify-center`}
                    onClick={() => setIsInfoDialogOpen(true)}
                    title="Show details"
                  >
                    <Info size={isMobile ? 20 : 18} />
                  </button>
                )}
                <button
                  className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-blue-600 rounded-lg flex items-center justify-center`}
                  onClick={handleDownload}
                  title="Download image"
                >
                  <Download size={isMobile ? 20 : 18} />
                </button>
                {onDelete && (
                  <button
                    className={`${isMobile ? 'p-2 min-w-[44px] min-h-[44px]' : 'p-1'} text-gray-400 hover:text-red-600 rounded-lg flex items-center justify-center`}
                    onClick={() => setIsDeleteDialogOpen(true)}
                    disabled={isDeleting}
                    title="Delete image"
                  >
                    <Trash2 size={isMobile ? 20 : 18} />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image Info Dialog */}
      {prompt && (
        <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
          <DialogContent className={`${isMobile ? 'max-w-[95vw] max-h-[90vh] overflow-auto' : ''}`}>
            <DialogHeader>
              <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Image Details</DialogTitle>
            </DialogHeader>
            <div className={`${isMobile ? 'space-y-4' : 'grid grid-cols-1 md:grid-cols-2 gap-4'}`}>
              <div className={`${isMobile ? 'w-full aspect-square' : 'aspect-square'} relative rounded-md overflow-hidden`}>
                <img
                  src={imageUrl}
                  alt="Generated image"
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="flex flex-col space-y-3">
                <div>
                  <h4 className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-500`}>Created on</h4>
                  <p className={`${isMobile ? 'text-base' : ''}`}>{formattedDate}</p>
                </div>
                <div>
                  <h4 className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-500`}>Prompt</h4>
                  <p className={`${isMobile ? 'text-sm leading-relaxed' : 'text-sm'} break-words mt-1`}>{prompt}</p>
                </div>
              </div>
            </div>
            <DialogFooter className={`${isMobile ? 'mt-6' : ''}`}>
              <button
                onClick={() => setIsInfoDialogOpen(false)}
                className={`${isMobile ? 'w-full py-3 text-base' : 'px-4 py-2 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors`}
              >
                Close
              </button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Confirm Deletion</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              Are you sure you want to delete this image?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'flex-col gap-3 mt-6' : ''}`}>
            <button
              onClick={() => setIsDeleteDialogOpen(false)}
              className={`${isMobile ? 'w-full py-3 text-base order-2' : 'px-4 py-2 text-sm'} bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors`}
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className={`${isMobile ? 'w-full py-3 text-base order-1' : 'px-4 py-2 text-sm'} bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors`}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Error Dialog */}
      <Dialog open={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Error</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              Failed to delete image. Please try again.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'mt-6' : ''}`}>
            <button
              onClick={() => setIsErrorDialogOpen(false)}
              className={`${isMobile ? 'w-full py-3 text-base' : 'px-4 py-2 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors`}
            >
              OK
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
