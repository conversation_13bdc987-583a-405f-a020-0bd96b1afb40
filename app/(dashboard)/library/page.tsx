"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import TabHeader from '../components/TabHeader';
import OptimizedImageGrid from './components/OptimizedImageGrid';
import OptimizedSkeletonGrid from './components/OptimizedSkeletonGrid';
import { CheckSquare } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';
import { useImageLibraryMetrics } from '@/lib/performance/imageLibraryMetrics';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Image {
  id: number;
  image_url: string;
  aspect_ratio?: string;
  created_at: string;
  prompt?: string;
}

interface LibraryState {
  images: Image[];
  hasMore: boolean;
  isLoading: boolean;
  error: string | null;
  nextCursor: string | null;
  selectedIds: number[];
  isSelectMode: boolean;
  isDeletingMultiple: boolean;
  isDeleteDialogOpen: boolean;
  deletionResult: {
    success: number;
    failed: number;
    showResult: boolean;
  };
}

export default function LibraryPage() {
  const [state, setState] = useState<LibraryState>({
    images: [],
    hasMore: true,
    isLoading: false,
    error: null,
    nextCursor: null,
    selectedIds: [],
    isSelectMode: false,
    isDeletingMultiple: false,
    isDeleteDialogOpen: false,
    deletionResult: { success: 0, failed: 0, showResult: false }
  });

  const { isMobile } = useResponsive();
  const metrics = useImageLibraryMetrics();
  const hasLoadedRef = useRef(false);
  

  /**
   * Optimized fetch function with cursor-based pagination and caching
   */
  const fetchImages = useCallback(async (cursor?: string, append: boolean = false) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      console.log(`[LibraryPage] Fetching images, cursor: ${cursor}, append: ${append}`);

      // Start performance monitoring for initial load
      if (!append && !hasLoadedRef.current) {
        metrics.startLoad();
      }

      const fetchStart = performance.now();
      const url = new URL('/api/images', window.location.origin);
      url.searchParams.set('limit', '20');
      if (cursor) {
        url.searchParams.set('cursor', cursor);
      }

      const response = await fetch(url.toString(), {
        headers: {
          'Cache-Control': 'max-age=300' // 5 minutes cache
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch images');
      }

      const data = await response.json();
      const fetchTime = performance.now() - fetchStart;
      
      // Record API performance
      metrics.recordAPI(fetchTime, data.images?.length || 0);
      console.log(`[LibraryPage] Fetched ${data.images?.length || 0} images, hasMore: ${data.hasMore}`);

      setState(prev => ({
        ...prev,
        images: append ? [...prev.images, ...data.images] : data.images,
        hasMore: data.hasMore,
        nextCursor: data.nextCursor,
        isLoading: false,
        // Clear selected IDs only on fresh load (not append)
        selectedIds: append ? prev.selectedIds : []
      }));

      // Record first image load for initial fetch
      if (!append && !hasLoadedRef.current && data.images?.length > 0) {
        hasLoadedRef.current = true;
        metrics.recordFirstImage();
        
        // Record all images loaded after a short delay to account for image loading
        setTimeout(() => {
          metrics.recordAllImages(data.images.length);
          metrics.recordMemory();
          metrics.logSummary();
        }, 100);
      }

    } catch (err) {
      console.error(`[LibraryPage] Error fetching images:`, err);
      setState(prev => ({
        ...prev,
        error: 'Failed to load images',
        isLoading: false
      }));
    }
  }, [metrics]); // Remove hasLoadedRef - refs don't need to be in dependency arrays

  /**
   * Load more images for infinite scroll
   */
  const loadMoreImages = useCallback(async () => {
    if (state.nextCursor && state.hasMore && !state.isLoading) {
      await fetchImages(state.nextCursor, true);
    }
  }, [state.nextCursor, state.hasMore, state.isLoading, fetchImages]);

  // Initial load
  useEffect(() => {
    fetchImages();
  }, [fetchImages]);

  /**
   * Optimized toggle functions with useCallback
   */
  const toggleSelectMode = useCallback(() => {
    setState(prev => ({
      ...prev,
      isSelectMode: !prev.isSelectMode,
      selectedIds: !prev.isSelectMode ? prev.selectedIds : []
    }));
    console.log("[LibraryPage] Toggled select mode");
  }, []);

  const toggleSelectAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedIds: prev.selectedIds.length === prev.images.length 
        ? [] 
        : prev.images.map(img => img.id)
    }));
    console.log("[LibraryPage] Toggled select all");
  }, []);

  const toggleImageSelection = useCallback((id: number) => {
    setState(prev => ({
      ...prev,
      selectedIds: prev.selectedIds.includes(id)
        ? prev.selectedIds.filter(imgId => imgId !== id)
        : [...prev.selectedIds, id]
    }));
    console.log(`[LibraryPage] Toggled selection for image ${id}`);
  }, []);

  const openDeleteDialog = useCallback(() => {
    if (state.selectedIds.length === 0) return;
    setState(prev => ({ ...prev, isDeleteDialogOpen: true }));
  }, [state.selectedIds.length]);

  /**
   * Optimized bulk delete with better error handling
   */
  const deleteSelectedImages = useCallback(async () => {
    if (state.selectedIds.length === 0) return;
    
    try {
      setState(prev => ({ ...prev, isDeletingMultiple: true }));
      console.log(`[LibraryPage] Deleting ${state.selectedIds.length} selected images`);

      // Delete in parallel with proper error handling
      const deletePromises = state.selectedIds.map(async (id) => {
        try {
          const response = await fetch(`/api/images/${id}`, { method: 'DELETE' });
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          return { id, success: true };
        } catch (err) {
          console.error(`[LibraryPage] Error deleting image ${id}:`, err);
          return { id, success: false };
        }
      });

      const results = await Promise.all(deletePromises);
      const successes = results.filter(r => r.success).length;
      const failures = results.filter(r => !r.success).length;
      
      console.log(`[LibraryPage] Deletion results: ${successes} succeeded, ${failures} failed`);

      // Update state optimistically
      const successfulIds = results.filter(r => r.success).map(r => r.id);
      setState(prev => ({
        ...prev,
        images: prev.images.filter(img => !successfulIds.includes(img.id)),
        selectedIds: [],
        isSelectMode: false,
        isDeletingMultiple: false,
        isDeleteDialogOpen: false,
        deletionResult: {
          success: successes,
          failed: failures,
          showResult: failures > 0 || successes > 0
        }
      }));
    } catch (err) {
      console.error('[LibraryPage] Error in bulk deletion:', err);
      setState(prev => ({
        ...prev,
        isDeletingMultiple: false,
        isDeleteDialogOpen: false,
        deletionResult: {
          success: 0,
          failed: prev.selectedIds.length,
          showResult: true
        }
      }));
    }
  }, [state.selectedIds]);

  /**
   * Handle individual image deletion
   */
  const handleImageDeleted = useCallback((id: number) => {
    setState(prev => ({
      ...prev,
      images: prev.images.filter(img => img.id !== id),
      selectedIds: prev.selectedIds.filter(selectedId => selectedId !== id)
    }));
    console.log(`[LibraryPage] Deleted image ${id} from state`);
  }, []);

  // Memoized values for performance
  const isAllSelected = useMemo(() => 
    state.selectedIds.length === state.images.length && state.images.length > 0,
    [state.selectedIds.length, state.images.length]
  );

  const canDelete = useMemo(() => 
    state.selectedIds.length > 0 && !state.isDeletingMultiple,
    [state.selectedIds.length, state.isDeletingMultiple]
  );

  return (
    <div className={`flex flex-col items-center ${isMobile ? 'px-3' : 'px-4'} pt-2 min-h-full`}>
      <TabHeader
        title="Image Library"
        subtitle="View and manage your generated images"
        alignment="center"
      />

      <div className="w-full max-w-7xl mt-4 flex-1">
        {/* Select Mode Controls */}
        {state.isSelectMode && (
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-between items-center'} mb-4 p-3 bg-gray-100 rounded-lg`}>
            <div className={`${isMobile ? 'text-base' : 'text-sm'} font-medium ${isMobile ? 'text-center' : ''}`}>
              {state.selectedIds.length} {state.selectedIds.length === 1 ? 'image' : 'images'} selected
            </div>
            <div className={`flex ${isMobile ? 'flex-col' : 'gap-2'} ${isMobile ? 'gap-2' : ''}`}>
              <button
                onClick={toggleSelectAll}
                className={`${isMobile ? 'w-full py-3 text-base' : 'px-3 py-1.5 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors flex items-center justify-center ${isMobile ? 'min-h-[44px]' : ''}`}
              >
                <CheckSquare className={`${isMobile ? 'w-5 h-5 mr-2' : 'w-4 h-4 mr-1'}`} />
                {isAllSelected ? 'Unselect All' : 'Select All'}
              </button>
              <button
                onClick={openDeleteDialog}
                className={`${isMobile ? 'w-full py-3 text-base' : 'px-3 py-1.5 text-sm'} bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors ${isMobile ? 'min-h-[44px]' : ''}`}
                disabled={!canDelete}
              >
                {state.isDeletingMultiple ? 'Deleting...' : 'Delete Selected'}
              </button>
              <button
                onClick={toggleSelectMode}
                className={`${isMobile ? 'w-full py-3 text-base' : 'px-3 py-1.5 text-sm'} bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors ${isMobile ? 'min-h-[44px]' : ''}`}
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Select Mode Toggle */}
        {!state.isSelectMode && state.images.length > 0 && (
          <div className={`flex ${isMobile ? 'justify-center' : 'justify-end'} mb-4`}>
            <button
              onClick={toggleSelectMode}
              className={`${isMobile ? 'w-full max-w-sm py-3 text-base' : 'px-3 py-1.5 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors ${isMobile ? 'min-h-[44px]' : ''}`}
            >
              Select Images
            </button>
          </div>
        )}

        {/* Error State */}
        {state.error && (
          <div className={`text-center ${isMobile ? 'py-8' : 'py-10'} text-red-600`}>
            <p className={`${isMobile ? 'text-base' : ''}`}>{state.error}</p>
            <button
              onClick={() => fetchImages()}
              className={`mt-4 ${isMobile ? 'w-full max-w-sm py-3 text-base' : 'px-4 py-2'} bg-[#FD2D55] text-white rounded-md hover:bg-[#FD2D55]/90 transition-colors ${isMobile ? 'min-h-[44px]' : ''}`}
            >
              Try Again
            </button>
          </div>
        )}

        {/* Content */}
        {!state.error && (
          <>
            {state.isLoading && state.images.length === 0 ? (
              <OptimizedSkeletonGrid />
            ) : (
              <OptimizedImageGrid
                images={state.images}
                selectedIds={state.selectedIds}
                isSelectMode={state.isSelectMode}
                onImageSelected={toggleImageSelection}
                onImageDeleted={handleImageDeleted}
                onLoadMore={loadMoreImages}
                hasMore={state.hasMore}
                isLoading={state.isLoading}
              />
            )}
          </>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={state.isDeleteDialogOpen} onOpenChange={(open) => 
        setState(prev => ({ ...prev, isDeleteDialogOpen: open }))
      }>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Confirm Deletion</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              {state.selectedIds.length === 1
                ? "Are you sure you want to delete this image?"
                : `Are you sure you want to delete these ${state.selectedIds.length} images?`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'flex-col gap-3 mt-6' : ''}`}>
            <button
              onClick={() => setState(prev => ({ ...prev, isDeleteDialogOpen: false }))}
              className={`${isMobile ? 'w-full py-3 text-base order-2' : 'px-4 py-2 text-sm'} bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors`}
            >
              Cancel
            </button>
            <button
              onClick={deleteSelectedImages}
              className={`${isMobile ? 'w-full py-3 text-base order-1' : 'px-4 py-2 text-sm'} bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors`}
              disabled={state.isDeletingMultiple}
            >
              {state.isDeletingMultiple ? "Deleting..." : "Delete"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Deletion Result Dialog */}
      <Dialog open={state.deletionResult.showResult} onOpenChange={(open) => 
        setState(prev => ({ 
          ...prev, 
          deletionResult: { ...prev.deletionResult, showResult: open } 
        }))
      }>
        <DialogContent className={`${isMobile ? 'max-w-[95vw]' : ''}`}>
          <DialogHeader>
            <DialogTitle className={`${isMobile ? 'text-lg' : ''}`}>Deletion Results</DialogTitle>
            <DialogDescription className={`${isMobile ? 'text-base leading-relaxed' : ''}`}>
              {state.deletionResult.success > 0 && state.deletionResult.failed > 0 ? (
                <>
                  Successfully deleted {state.deletionResult.success} images.
                  <br />
                  Failed to delete {state.deletionResult.failed} images. Please try again.
                </>
              ) : state.deletionResult.success > 0 ? (
                `Successfully deleted ${state.deletionResult.success} images.`
              ) : (
                "Failed to delete images. Please try again."
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className={`${isMobile ? 'mt-6' : ''}`}>
            <button
              onClick={() => setState(prev => ({ 
                ...prev, 
                deletionResult: { ...prev.deletionResult, showResult: false } 
              }))}
              className={`${isMobile ? 'w-full py-3 text-base' : 'px-4 py-2 text-sm'} bg-black text-white rounded-md hover:bg-gray-800 transition-colors`}
            >
              OK
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}